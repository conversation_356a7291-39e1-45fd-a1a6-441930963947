<script setup lang="ts" name="AppDownload">
import { ref, onMounted, computed } from "vue";
import { getAppDownloadConfig, getAppDownloadLinks } from "../../service";
import Loading from "../../components/Loading.vue";
import { getQuery, deviceDetector, appDownloader } from "../../utils";
import { envConfig } from "../../utils/env";

// 渠道枚举
enum CHANEL_TERMINAL {
  "GP" = 1,
  "H5" = 2, // otherApp
  "iOS" = 4,
  "Gcash" = 8,
  "Maya" = 64,
  "Web" = 128,
}

// 页面配置数据类型
interface DownloadConfig {
  picture_url: string;
  slogan: string;
  iosButton: {
    ios_switch: boolean;
    text: string;
    storeUrl: string;
  };
  androidButton: {
    android_switch: boolean;
    text: string;
    storeUrl: string;
  };
}

// 响应式数据
const loading = ref(false);
const config = ref<DownloadConfig | null>(null);
const deviceType = ref(deviceDetector.getDeviceType());
const isDownloading = ref(false);

const hasTwoBtns = computed(() => {
  return config.value?.iosButton.ios_switch && config.value?.androidButton.android_switch;
});

const assetsUrl = (): string => envConfig.assetsUrl;

// 初始化页面数据
async function initPageData() {
  loading.value = true;
  // 尝试请求配置数据，如果失败则使用默认配置
  try {
    const query = getQuery(window.location.search);
    const channel = query.channel;
    const channelKey = channel ? Object.keys(CHANEL_TERMINAL).find((key) => key.toUpperCase() === String(channel).toUpperCase()) : undefined;
    const channel_type: number = CHANEL_TERMINAL[channelKey as keyof typeof CHANEL_TERMINAL] || 128;
    const res = await getAppDownloadConfig({ channel_type });
    console.log("尝试请求配置数据，如果失败则使用默认配置", res);
    if (res.code === 0 && res.data) {
      config.value = {
        picture_url: res.data.picture_url ? assetsUrl() + res.data.picture_url : "",
        slogan: res.data.slogan,
        iosButton: {
          ios_switch: res.data.ios_switch,
          text: res.data.iosButtonText || "Download on the iOS",
          storeUrl: res.data.ios_download_url,
        },
        androidButton: {
          android_switch: res.data.android_switch,
          text: res.data.androidButtonText || "Download on the Android",
          storeUrl: res.data.android_download_url,
        },
      };
    } else {
      // API返回成功但数据为空，或者返回错误码，使用默认配置
      config.value = getDefaultConfig();
      console.log("API返回数据为空或错误，使用默认配置", config.value);
    }
  } catch (apiError) {
    console.log("API not available, using default config");
    // 使用默认配置
    config.value = getDefaultConfig();
  } finally {
    loading.value = false;
  }
}

// 获取默认配置
function getDefaultConfig(): DownloadConfig {
  return {
    picture_url: "",
    slogan: `Download the app & claim your free bonus instantly! Daily rewards, big wins`,
    iosButton: {
      ios_switch: false,
      text: "Download on the iOS",
      storeUrl: "https://apps.apple.com/cn/app/%25E6%2590%259C%25E7%258B%2597%25E8%25BE%2593%25E5%2585%25A5%25E6%25B3%2595/id917670924",
    },
    androidButton: {
      android_switch: false,
      text: "Download on the Android",
      storeUrl: "https://appgallery.huawei.com/#/app/C100214355",
    },
  };
}

// 处理下载按钮点击
async function handleDownload(platform: "ios" | "android") {
  if (isDownloading.value || !config.value) return;

  isDownloading.value = true;

  try {
    const buttonConfig = platform === "ios" ? config.value.iosButton : config.value.androidButton;
    if (!buttonConfig.storeUrl) return;
    // 首先尝试打开应用商城
    const storeSuccess = await appDownloader.openAppStore(buttonConfig.storeUrl, 3000);
    if (!storeSuccess) {
      // 应用商城打开失败
      appDownloader.showToast("Download failed, please try again.");
    }
  } catch (error) {
    console.error("Download error:", error);
    appDownloader.showToast("Download failed, please try again.");
  } finally {
    isDownloading.value = false;
  }
}

// 页面挂载时初始化
onMounted(() => {
  initPageData();
});
</script>

<template>
  <Loading v-if="loading" />
  <div v-else class="download-page">
    <!-- 主要内容区域 -->
    <div class="content-area" :style="{ 'padding-bottom': hasTwoBtns ? '152px' : '100px' }">
      <img v-if="config?.picture_url" :src="config?.picture_url" alt="" />
      <!-- 背景图片内容由CSS背景显示 -->
    </div>

    <!-- 底部固定下载模块 -->
    <div class="download-section">
      <!-- 下载按钮容器 -->
      <div class="download-buttons">
        <!-- iOS 下载按钮 -->
        <button class="download-btn ios-btn" v-if="config?.iosButton.ios_switch" :class="{ downloading: isDownloading }" :disabled="isDownloading" @click="handleDownload('ios')">
          <div class="btn-content">
            <div class="btn-icon">
              <img src="@/assets/img/download/iOS.png" alt="" />
            </div>
            <div :class="config?.androidButton.android_switch ? '' : 'btn-text'">
              <div class="btn-title">Download on the</div>
              <div class="btn-subtitle">IOS</div>
            </div>
          </div>
        </button>

        <!-- Android 下载按钮 -->
        <button class="download-btn android-btn" v-if="config?.androidButton.android_switch" :class="{ downloading: isDownloading }" :disabled="isDownloading" @click="handleDownload('android')">
          <div class="btn-content">
            <div class="btn-icon">
              <img src="@/assets/img/download/Android.png" alt="" />
            </div>
            <div :class="config?.iosButton.ios_switch ? '' : 'btn-text'">
              <div class="btn-title">Download on the</div>
              <div class="btn-subtitle">Android</div>
            </div>
          </div>
        </button>
      </div>

      <!-- 标语文本 -->
      <div class="slogan-text">
        {{ config?.slogan }}
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.download-page {
  font-family: "Inter";
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  min-height: 100dvh;
  position: relative;
  overflow: hidden;
}

.content-area {
  flex: 1;
  position: relative;
  min-height: 100vh;
  img {
    width: 100%;
    height: auto;
  }
}

.download-section {
  font-family: "Inter";
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20px;
  z-index: 100;
  width: 100%;
  opacity: 1;
  gap: 12px;
  box-shadow: #222222;
}

.download-buttons {
  display: flex;
  gap: 12px;
  // 确保按钮始终并排，不换行
  flex-wrap: nowrap;
  width: 100%;
}

.download-btn {
  flex: 1;
  background: #222222;
  border-radius: 30px;
  width: 163px;
  height: 60px;
  color: #fff;
  margin-bottom: 20px;
}

.btn-content {
  padding: 0 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  > img {
    width: 28px;
    height: 28px;
  }
}
// 仅显示一个按钮时
.btn-text {
  display: flex;
  justify-content: center;
  align-content: center;
  gap: 4px;
  margin-left: 8px;
  .btn-title,
  .btn-subtitle {
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    text-align: center;
  }
}

.btn-title {
  font-weight: 600;
  font-size: 12px;
  line-height: 24px;
  text-align: center;
}

.btn-subtitle {
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0%;
  text-align: center;
}

.slogan-text {
  font-weight: 400;
  font-size: 13px;
  line-height: 20px;
  letter-spacing: 0%;
  text-align: center;
  color: #666;
}
</style>
